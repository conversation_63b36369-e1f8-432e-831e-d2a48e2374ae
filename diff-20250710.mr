diff --git a/searchsvr/common/apollo/searchengine.go b/searchsvr/common/apollo/searchengine.go
index b7d6ccb12..331332032 100644
--- a/searchsvr/common/apollo/searchengine.go
+++ b/searchsvr/common/apollo/searchengine.go
@@ -96,6 +96,7 @@ type SearchApolloConfig struct {
 	MainSiteDishTruncTextMatchScore   float64 `xml:"MainSiteDishTruncTextMatchScore"`   // dishSort的时候，DishTextMatchScore 的 截断分数
 	MainSiteDumpLogKafkaTopicName     string  `xml:"MainSiteDumpLogKafkaTopicName"`     // 主站 dumplog 发送的 topicName
 	MainSiteDumpLogCutSize            int     `xml:"MainSiteDumpLogCutSize"`            // 主站 dumplog 发送的 cutSize，默认 200
+	MainSiteVNRoutingDistanceLimit    int     `xml:"MainSiteVNRoutingDistanceLimit"`    // VN 主站routing distance 过滤
 
 	CloseSearchStoresAffiliateCache bool `xml:"CloseSearchStoresAffiliateCache"` // 关闭门店下搜菜缓存
 	CloseSearchDishesAffiliateCache bool `xml:"CloseSearchDishesAffiliateCache"` // 关闭门店下搜菜缓存
diff --git a/searchsvr/common/constants/distance.go b/searchsvr/common/constants/distance.go
index 431e029fd..1f56f4f24 100644
--- a/searchsvr/common/constants/distance.go
+++ b/searchsvr/common/constants/distance.go
@@ -2,8 +2,9 @@ package constants
 
 import "git.garena.com/shopee/o2o-intelligence/shopeefoodads/common-lib/cid"
 
-var MaxDeliveryDistance uint32 = 20000 // 20km
-var MaxPickupDistance uint32 = 100000  // 100km
+var MaxDeliveryDistance uint32 = 20000            // 20km
+var MaxDeliveryDistanceForMainSite uint32 = 20000 // 20km
+var MaxPickupDistance uint32 = 100000             // 100km
 
 func init() {
 	if cid.IsVN() {
diff --git a/searchsvr/common/traceinfo/phrase_store_len.go b/searchsvr/common/traceinfo/phrase_store_len.go
index 1414ec43a..6375d7c9f 100644
--- a/searchsvr/common/traceinfo/phrase_store_len.go
+++ b/searchsvr/common/traceinfo/phrase_store_len.go
@@ -9,19 +9,20 @@ import (
 
 const (
 	// 各阶段门店数
-	StoreLenMerge                = "StoreLenMerge"
-	StoreLenMergeNer             = "StoreLenMergeNer"
-	StoreLenMergeI2I             = "StoreLenMergeI2I"
-	StoreLenFilling              = "StoreLenFilling"
-	StoreLenFilter               = "StoreLenFilter"
-	StoreLenFilterDistanceVN     = "StoreLenFilterDistanceVN"
-	StoreLenFilterDuplicateBrand = "StoreLenFilterDuplicateBrand"
-	StoreLenFinal                = "StoreLenFinal"
-	StoreLenMixer                = "StoreLenMixer"
-	StoreLenIntervention         = "StoreLenIntervention"
-	StoreLenAds                  = "StoreLenAds"
-	StoreLenCoarseRank           = "StoreLenCoarseRank" // 粗排
-	StoreLenLWDistinct           = "StoreLenLWDistinct"
+	StoreLenMerge                    = "StoreLenMerge"
+	StoreLenMergeNer                 = "StoreLenMergeNer"
+	StoreLenMergeI2I                 = "StoreLenMergeI2I"
+	StoreLenFilling                  = "StoreLenFilling"
+	StoreLenFilter                   = "StoreLenFilter"
+	StoreLenFilterDistanceVN         = "StoreLenFilterDistanceVN"
+	StoreLenFilterDistanceVNMainSite = "StoreLenFilterDistanceVNMainSite"
+	StoreLenFilterDuplicateBrand     = "StoreLenFilterDuplicateBrand"
+	StoreLenFinal                    = "StoreLenFinal"
+	StoreLenMixer                    = "StoreLenMixer"
+	StoreLenIntervention             = "StoreLenIntervention"
+	StoreLenAds                      = "StoreLenAds"
+	StoreLenCoarseRank               = "StoreLenCoarseRank" // 粗排
+	StoreLenLWDistinct               = "StoreLenLWDistinct"
 
 	// 某些情况下的门店数量
 	StoreCntOpeningStoreBeforeFilter = "StoreCntOpeningStoreBeforeFilter"
diff --git a/searchsvr/common/traceinfo/trace_service.go b/searchsvr/common/traceinfo/trace_service.go
index 71d17d68c..350b27f35 100644
--- a/searchsvr/common/traceinfo/trace_service.go
+++ b/searchsvr/common/traceinfo/trace_service.go
@@ -409,6 +409,102 @@ func BuildTraceInfo(ctx context.Context, req *foodalgo_search.SearchRequest, han
 	return traceInfo
 }
 
+func BuildTraceInfoForMainSite(ctx context.Context, req *foodalgo_search.SearchRequest, handlerType HandlerType) *TraceInfo {
+	spanContext := tracing.GetSpanContext(ctx)
+	isShadow := tracing.IsSpanContextShadow(spanContext)
+
+	traceInfo := NewTraceInfo()
+	//traceInfo.OriginRequest = req
+	traceInfo.TraceRequest.PublishId = req.GetPublishId()
+	traceInfo.TraceRequest.QueryRaw = req.GetKeyword() // 最原始keyword，后续不会改动
+	traceInfo.QueryKeyword = req.GetKeyword()          // 实际搜索词，可能有多处修改，例如predefine/纠错/ascii mapping 等等
+	traceInfo.TraceRequest.Longitude = float64(req.GetLongitude())
+	traceInfo.TraceRequest.Latitude = float64(req.GetLatitude())
+	traceInfo.TraceRequest.PageNum = req.GetPageNum()
+	traceInfo.TraceRequest.PageSize = req.GetPageSize()
+	traceInfo.TraceRequest.SortType = req.GetSortType().Enum()
+	traceInfo.TraceRequest.FilterType = req.GetFilterType()
+	traceInfo.TraceRequest.SearchTime = req.GetSearchTime()
+	traceInfo.TraceRequest.CityId = req.GetCityId()
+	traceInfo.TraceRequest.DistrictIds = req.GetDistrictIds()
+	traceInfo.TraceRequest.LocationGroupIds = req.GetLocationGroupIds()
+	traceInfo.TraceRequest.AppType = req.GetAppType()
+	traceInfo.TraceRequest.FocusServiceId = req.GetFocusServiceId()
+	traceInfo.TraceRequest.FoodyServiceIds = req.GetVnPromotionParams().GetFoodyServiceIds()
+	traceInfo.TraceRequest.ExtClientInfo = req.GetClientExtraInfo()
+	traceInfo.SceneId = req.GetSceneId()
+
+	if apollo.SearchApolloCfg.MainSiteVNRoutingDistanceLimit == 0 {
+		traceInfo.DistanceLimit = constants.MaxDeliveryDistanceForMainSite // 主站默认只要20km
+	} else {
+		traceInfo.DistanceLimit = uint32(apollo.SearchApolloCfg.MainSiteVNRoutingDistanceLimit)
+	}
+
+	traceInfo.UserId = req.GetBuyerId()
+	traceInfo.IsNeedCorrect = req.GetNeedRewrite()
+	traceInfo.IsDebug = req.GetIsDebug() || req.GetSearchDebugReq().GetTrafficFlag() == foodalgo_search.SearchDebugReq_Debug
+	traceInfo.SearchDebugReq = req.GetSearchDebugReq()
+	if traceInfo.SearchDebugReq == nil {
+		traceInfo.SearchDebugReq = &foodalgo_search.SearchDebugReq{}
+	}
+	if traceInfo.SearchDebugReq.DebugSwitch == nil && req.GetDebugSwitch() != nil {
+		traceInfo.SearchDebugReq.DebugSwitch = req.GetDebugSwitch()
+	}
+
+	traceInfo.ShadowFlag = isShadow
+	traceInfo.HandlerType = handlerType
+
+	buildUserContext(ctx, req, traceInfo)
+	abTest := req.GetAbTest()
+	if len(req.GetSearchDebugReq().GetAbTest()) > 0 {
+		abTest = req.GetSearchDebugReq().GetAbTest()
+	}
+	buildABTest(ctx, traceInfo, req.GetBuyerId(), abTest)
+	req.AbTest = proto.String(traceInfo.ABTestGroup.GetABTestString())
+	traceInfo.AddPredictConfigToTraceInfo()
+	buildEsClientAndIndex(ctx, traceInfo)
+	buildRspHeaders(ctx, traceInfo)
+	traceInfo.MetaPool = data_management_client.NewMetaPool(data_management_client.MetaPoolConfig{DishMetaEnable: true, DishMetaBufSize: 2000, FlashSaleDishDiscountEnable: true, FlashSaleDishDiscountBufSize: 1024})
+	if traceInfo.IsDebug == true && req.GetDebugSwitch() != nil {
+		traceInfo.IsSkipModel = req.GetDebugSwitch().GetIsSkipModel()
+		traceInfo.IsSkipAds = req.GetDebugSwitch().GetIsSkipAds()
+		if len(req.GetDebugSwitch().GetEsExplainStoreIds()) > 0 {
+			traceInfo.IsNeedEsExplain = true
+			traceInfo.EsExplainStoreIds = make(map[uint64]bool, len(req.GetDebugSwitch().GetEsExplainStoreIds()))
+			for _, storeId := range req.GetDebugSwitch().GetEsExplainStoreIds() {
+				traceInfo.EsExplainStoreIds[storeId] = true
+			}
+		}
+	}
+	if req.GetDowngrade() != nil {
+		downgrade := req.GetDowngrade()
+		level := ""
+		l1 := downgrade.GetMethodDownGrade_1()
+		l2 := downgrade.GetMethodDownGrade_2()
+		l3 := downgrade.GetMethodDownGrade_3()
+		l4 := downgrade.GetMethodDownGrade_4()
+		if l1 != "" && l1 != MethodDownGradeNoNeed && l1 != MethodDownGradeNotConfig {
+			level = l1
+		} else if l2 != "" && l2 != MethodDownGradeNoNeed && l2 != MethodDownGradeNotConfig {
+			level = l2
+		} else if l3 != "" && l3 != MethodDownGradeNoNeed && l3 != MethodDownGradeNotConfig {
+			level = l3
+		} else if l4 != "" && l4 != MethodDownGradeNoNeed && l4 != MethodDownGradeNotConfig {
+			level = l4
+		}
+		traceInfo.DowngradeLevel = level
+	}
+
+	// 品牌保护
+	if cid.IsVN() {
+		traceInfo.IsHitBrandProtection = vn_brand_protection_keywords.GetVnBrandProtectionKeywords(traceInfo.QueryKeyword)
+		if len(req.GetFilterType().GetCategoryType()) == 1 && req.GetFilterType().GetCategoryType()[0] == 1002 {
+			traceInfo.IsVnMart = true
+		}
+	}
+	return traceInfo
+}
+
 func BuildSearchStoresForAffiliateTraceInfo(ctx context.Context, req *foodalgo_search.SearchStoresAffiliateReq, handlerType HandlerType) *TraceInfo {
 	spanContext := tracing.GetSpanContext(ctx)
 	isShadow := tracing.IsSpanContextShadow(spanContext)
diff --git a/searchsvr/handler/search_handler.go b/searchsvr/handler/search_handler.go
index 45ca9acef..c852977b8 100644
--- a/searchsvr/handler/search_handler.go
+++ b/searchsvr/handler/search_handler.go
@@ -140,7 +140,7 @@ func (s *SearchHandler) SearchForMainSite(ctx context.Context, req *foodalgo_sea
 		BuyerId:          proto.Uint64(req.GetShopeeUid()),
 		SearchDebugReq:   req.SearchDebugReq,
 	}
-	traceInfo := traceinfo.BuildTraceInfo(ctx, reqMain, handlerType)
+	traceInfo := traceinfo.BuildTraceInfoForMainSite(ctx, reqMain, handlerType)
 	defer traceInfo.CloseTraceInfo()
 	debugInfo := debuginfo.InitCombineDebugInfo(traceInfo)
 	defer func() {
diff --git a/searchsvr/processor/filter/filter_distance_vn.go b/searchsvr/processor/filter/filter_distance_vn.go
index 1dcfd1222..3ec7616a3 100644
--- a/searchsvr/processor/filter/filter_distance_vn.go
+++ b/searchsvr/processor/filter/filter_distance_vn.go
@@ -26,7 +26,35 @@ func DistanceFilterVN(ctx context.Context, traceInfo *traceinfo.TraceInfo, store
 		if isStoreDistanceValid(traceInfo, store, distance) {
 			resStores = append(resStores, store)
 		} else {
-			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeStoreDistance, store.Distance)
+			if traceInfo.IsDebug {
+				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeStoreDistance, store.Distance)
+			}
+		}
+	}
+	return resStores
+}
+
+func DistanceFilterVNMainSite(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo, distance uint32) []*model.StoreInfo {
+	if len(stores) == 0 {
+		return stores
+	}
+	if traceInfo.IsDowngradeDataServer {
+		return stores
+	}
+	if util.IsZeroFloat64(traceInfo.TraceRequest.Longitude) && util.IsZeroFloat64(traceInfo.TraceRequest.Latitude) {
+		return stores
+	}
+	resStores := make([]*model.StoreInfo, 0, len(stores))
+	defer func() {
+		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFilterDistanceVNMainSite, len(resStores))
+	}()
+	for _, store := range stores {
+		if store.Distance <= float64(distance) {
+			resStores = append(resStores, store)
+		} else {
+			if traceInfo.IsDebug {
+				traceInfo.AddFilteredStore(store.StoreId, traceinfo.StoreLenFilterDistanceVNMainSite, store.Distance)
+			}
 		}
 	}
 	return resStores
diff --git a/searchsvr/processor/filter/fiter.go b/searchsvr/processor/filter/fiter.go
index 79104a3ea..15f67ce33 100644
--- a/searchsvr/processor/filter/fiter.go
+++ b/searchsvr/processor/filter/fiter.go
@@ -143,6 +143,7 @@ func SearchMainSiteFilter(ctx context.Context, stores []*model.StoreInfo, traceI
 	// 正排降级时不进行过滤
 	if !traceInfo.IsDowngradeDataServer {
 		if cid.IsVNRegion() {
+			stores = DistanceFilterVNMainSite(ctx, traceInfo, stores, traceInfo.DistanceLimit)
 			stores = DuplicateBrandFilter(ctx, traceInfo, stores)
 		} else {
 			stores = StorePolygonDistanceFilter(ctx, traceInfo, stores)
