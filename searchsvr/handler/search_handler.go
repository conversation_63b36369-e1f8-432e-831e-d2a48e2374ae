package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/trace"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/paramcheck"
)

type SearchHandler struct{}

func (s *SearchHandler) Search(ctx context.Context, req *foodalgo_search.SearchRequest) (*foodalgo_search.SearchResponse, error) {
	rsp := &foodalgo_search.SearchResponse{}
	startTime := time.Now()
	handlerType := traceinfo.GetHandlerType(req)
	// 参数校验
	err, invalid := paramcheck.SearchParamCheckInValid(ctx, req, handlerType)
	if invalid {
		rsp.ErrCode = proto.Uint64(uint64(errno.ErrParamsInvalid.GetCode()))
		if err != nil {
			rsp.ErrMsg = proto.String(err.Error())
		} else {
			rsp.ErrMsg = proto.String(errno.ErrParamsInvalid.GetMsg())
		}
		return rsp, nil
	}
	// 对 query截断，超过max都不是合理的请求
	if len(req.GetKeyword()) > apollo.GetMaxQueryLen() {
		logkit.FromContext(ctx).Error("Search params invalid query len", logkit.String("keyword", req.GetKeyword()), logkit.Int("len", len(req.GetKeyword())))
		req.Keyword = proto.String(req.GetKeyword()[:apollo.GetMaxQueryLen()])
	}

	traceInfo := traceinfo.BuildTraceInfo(ctx, req, handlerType)
	defer traceInfo.CloseTraceInfo()

	debugInfo := debuginfo.InitCombineDebugInfo(traceInfo)
	// 广告查询词也需要处理，只能放在最外层，不能放到preProcessor. 为了给ads使用,此处是唯一会修改req中keyword的地方
	if env.GetCID() == cid.VN {
		if len(req.GetKeyword()) > 0 {
			req.Keyword = proto.String(preprocess.UnicodeReplace(req.GetKeyword()))
			traceInfo.QueryKeyword = req.GetKeyword()
		}
		preprocess.VNDistanceLimit(ctx, traceInfo)
		preprocess.VnBrandProtectStoreIds(ctx, traceInfo)
	}
	defer func() {
		rsp.ContextHeaders = traceInfo.RspHeaders
		if len(rsp.GetIds()) > 0 {
			rsp.ErrMsg = proto.String(errno.Ok.Msg)
			rsp.ErrCode = proto.Uint64(uint64(errno.Ok.Code))
		}
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeServerHandler, traceInfo.HandlerType.String(), "", decision.GetReportHandlerString(traceInfo))
		storeNum, dishNum, adsStoreNum, adsDishNum := getItemNums(rsp.GetIds())
		metric_reporter2.ReportFinalItemNum(storeNum, dishNum, adsStoreNum, adsDishNum, metric_reporter2.SearchReportTypeResponse, traceInfo.HandlerType.String(), "")
		debugInfo.FillProcessInfo(traceInfo)
		rsp.DebugCombineInfo = debugInfo.ToString(ctx, traceInfo)
		rsp.SlaCode = proto.Uint64(traceInfo.SlaCode.GetCode())
		goroutine.WithGo(ctx, "SPFSRSearch Log", func(params ...interface{}) {
			reqStr, _ := json.Marshal(req)
			resStr, _ := json.Marshal(rsp)

			logkit.FromContext(ctx).Info("SPFSRSearch", zap.String("SearchType", traceInfo.HandlerType.String()),
				zap.String("sla_code", traceInfo.SlaCode.GetCodeStr()),
				zap.Int("cost", int(time.Since(traceInfo.SearchSysTime)/1e6)),
				zap.Any("request", string(reqStr)), zap.Any("response", string(resStr)), zap.Any("ATP", traceInfo.ATP),
				zap.Bool("real_shadow_flag", traceInfo.ShadowFlag), zap.String("method_downgrade_level", traceInfo.DowngradeLevel))
			if traceInfo.IsDebug {
				traceInfo.DebugInfo = nil
				logger.MyDebug(ctx, traceInfo.IsDebug, "Debug TraceInfo", zap.Any("trace info", traceInfo))
			}
		})
	}()

	switch traceInfo.HandlerType {
	case traceinfo.HandlerTypeSearch, traceinfo.HandlerTypeSearchFood, traceinfo.HandlerTypeSearchMart:
		err = SPFSearch(ctx, req, rsp, traceInfo, debugInfo)
	case traceinfo.HandlerTypeSearchGlobal:
		err = SearchGlobalForVN(ctx, req, rsp, traceInfo, debugInfo) // vn 不分页，并行调用MixerProcessMerge
	case traceinfo.HandlerTypeSearchIdsStores, traceinfo.HandlerTypeSearchIdsWeb, traceinfo.HandlerTypeSearchIdsFoody:
		err = SearchIdsForVN(ctx, req, rsp, traceInfo, debugInfo)
	case traceinfo.HandlerTypeSearchGlobalV1, traceinfo.HandlerTypeSearchIdsDishes:
		err = SearchGlobalV1(ctx, req, rsp, traceInfo, debugInfo) // vn 不分页旧接口，根据foody_service_id返回
	default:
		logkit.FromContext(ctx).Error("params scene id invalid", logkit.String("req", req.String()))
		err = errno.ErrParamsInvalid
	}
	errNo := errno.As(err)
	rsp.ErrCode = proto.Uint64(uint64(errNo.GetCode()))
	rsp.ErrMsg = proto.String(errNo.GetMsg())
	if traceInfo.ATP != 0 {
		rsp.Atp = proto.Int(traceInfo.ATP)
	}
	return rsp, nil
}

func (s *SearchHandler) SearchForMainSite(ctx context.Context, req *foodalgo_search.SearchForMainSiteRequest) (*foodalgo_search.SearchForMainSiteResponse, error) {
	rsp := &foodalgo_search.SearchForMainSiteResponse{}
	handlerType := traceinfo.HandlerTypeSearchMainSite
	startTime := time.Now()
	// 参数校验
	if len(req.GetKeyword()) == 0 || req.Longitude == nil || req.Latitude == nil || req.SortBy == nil {
		logkit.FromContext(ctx).Error("SearchForMainSite params invalid", logkit.String("req", req.String()))
		return rsp, errno.ErrParamsInvalid
	}
	// 对 query截断，超过max都不是合理的请求
	if len(req.GetKeyword()) > apollo.GetMaxQueryLen() {
		logkit.FromContext(ctx).Error("SearchForMainSite params invalid query len", logkit.String("keyword", req.GetKeyword()), logkit.Int("len", len(req.GetKeyword())))
		req.Keyword = proto.String(req.GetKeyword()[:apollo.GetMaxQueryLen()])
	}

	reqMain := &foodalgo_search.SearchRequest{
		Keyword:          req.Keyword,
		Longitude:        req.Longitude,
		Latitude:         req.Latitude,
		SortType:         foodalgo_search.SearchRequest_Relevance.Enum(),
		PublishId:        proto.String(trace.RequestIDFromContext(ctx)), // 这里用 logId 来填充主站的 publish_id
		LocationGroupIds: req.GetLocationGroupIds(),
		BuyerId:          proto.Uint64(req.GetShopeeUid()),
		SearchDebugReq:   req.SearchDebugReq,
	}
	traceInfo := traceinfo.BuildTraceInfoForMainSite(ctx, reqMain, handlerType)
	defer traceInfo.CloseTraceInfo()
	debugInfo := debuginfo.InitCombineDebugInfo(traceInfo)
	defer func() {
		rsp.ContextHeaders = traceInfo.RspHeaders
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeServerHandler, traceInfo.HandlerType.String(), "", decision.GetReportHandlerString(traceInfo))
		debugInfo.FillProcessInfo(traceInfo)
		rsp.DebugCombineInfo = debugInfo.ToString(ctx, traceInfo)
		rsp.SlaCode = proto.Uint64(traceInfo.SlaCode.GetCode())
		goroutine.WithGo(ctx, "SPFSRSearchMainSite Log", func(params ...interface{}) {
			reqStr, _ := json.Marshal(req)
			resStr, _ := json.Marshal(rsp)
			logkit.FromContext(ctx).Info("SPFSRSearchMainSite", zap.String("SearchType", traceInfo.HandlerType.String()),
				zap.String("sla_code", traceInfo.SlaCode.GetCodeStr()),
				zap.Int("cost", int(time.Since(traceInfo.SearchSysTime)/1e6)),
				zap.Any("request", string(reqStr)), zap.Any("response", string(resStr)),
				zap.Bool("real_shadow_flag", traceInfo.ShadowFlag))
			traceInfo.DebugInfo = nil
			rsp.DebugCombineInfo = debugInfo.ToString(ctx, traceInfo)
			logger.MyDebug(ctx, traceInfo.IsDebug, "Debug TraceInfo", zap.Any("trace info", traceInfo))
		})
	}()
	err := SearchMainSite(ctx, reqMain, rsp, traceInfo, debugInfo, req.GetSortBy())
	return rsp, err
}

func (s *SearchHandler) SearchCollection(ctx context.Context, req *foodalgo_search.SearchRequest) (*foodalgo_search.SearchResponse, error) {
	rsp := &foodalgo_search.SearchResponse{}
	handlerType := traceinfo.HandlerTypeSearchCollection
	startTime := time.Now()
	// 参数校验
	if len(req.GetKeyword()) == 0 || req.Longitude == nil || req.Latitude == nil || req.SortType == nil {
		logkit.FromContext(ctx).Error("SearchForCollection params invalid", logkit.String("req", req.String()))
		return rsp, errno.ErrParamsInvalid
	}
	// 对 query截断，超过max都不是合理的请求
	if len(req.GetKeyword()) > apollo.GetMaxQueryLen() {
		logkit.FromContext(ctx).Error("SearchCollection params invalid query len", logkit.String("keyword", req.GetKeyword()), logkit.Int("len", len(req.GetKeyword())))
		req.Keyword = proto.String(req.GetKeyword()[:apollo.GetMaxQueryLen()])
	}

	traceInfo := traceinfo.BuildTraceInfo(ctx, req, handlerType)
	defer traceInfo.CloseTraceInfo()
	debugInfo := debuginfo.InitCombineDebugInfo(traceInfo)
	defer func() {
		rsp.ContextHeaders = traceInfo.RspHeaders
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeServerHandler, traceInfo.HandlerType.String(), "", decision.GetReportHandlerString(traceInfo))
		debugInfo.FillProcessInfo(traceInfo)
		rsp.DebugCombineInfo = debugInfo.ToString(ctx, traceInfo)
		rsp.SlaCode = proto.Uint64(traceInfo.SlaCode.GetCode())
		goroutine.WithGo(ctx, "SearchForCollection Log", func(params ...interface{}) {
			reqStr, _ := json.Marshal(req)
			resStr, _ := json.Marshal(rsp)

			logkit.FromContext(ctx).Info("SearchForCollection", zap.String("SearchType", traceInfo.HandlerType.String()),
				zap.String("sla_code", traceInfo.SlaCode.GetCodeStr()),
				zap.Int("cost", int(time.Since(traceInfo.SearchSysTime)/1e6)),
				zap.Any("request", string(reqStr)), zap.Any("response", string(resStr)),
				zap.Bool("real_shadow_flag", traceInfo.ShadowFlag), zap.String("method_downgrade_level", traceInfo.DowngradeLevel))
			logger.MyDebug(ctx, traceInfo.IsDebug, "Debug TraceInfo", zap.Any("trace info", traceInfo))
		})
	}()
	err := SearchCollection(ctx, req, rsp, traceInfo, debugInfo)
	// ATP
	errNo := errno.As(err)
	rsp.ErrCode = proto.Uint64(uint64(errNo.GetCode()))
	rsp.ErrMsg = proto.String(errNo.GetMsg())
	if traceInfo.ATP != 0 {
		rsp.Atp = proto.Int(traceInfo.ATP)
	}
	return rsp, nil
}

func (s *SearchHandler) SearchTotalNum(ctx context.Context, req *foodalgo_search.SearchRequest) (*foodalgo_search.SearchTotalNumResponse, error) {
	rsp := &foodalgo_search.SearchTotalNumResponse{}
	handlerType := traceinfo.GetTotalNumHandlerType(req)
	req.PublishId = proto.String(fmt.Sprintf("%s_%s", handlerType.String(), req.GetPublishId()))
	startTime := time.Now()
	// 参数校验
	err, invalid := paramcheck.SearchParamCheckInValid(ctx, req, handlerType)
	if invalid {
		return rsp, err
	}
	// 对 query截断，超过max都不是合理的请求
	if len(req.GetKeyword()) > apollo.GetMaxQueryLen() {
		logkit.FromContext(ctx).Error("SearchTotalNum params invalid query len", logkit.String("keyword", req.GetKeyword()), logkit.Int("len", len(req.GetKeyword())))
		req.Keyword = proto.String(req.GetKeyword()[:apollo.GetMaxQueryLen()])
	}

	traceInfo := traceinfo.BuildTraceInfo(ctx, req, handlerType)
	defer traceInfo.CloseTraceInfo()
	debugInfo := debuginfo.InitCombineDebugInfo(traceInfo)
	// 广告查询词也需要处理，只能放在最外层，不能放到preProcessor. 为了给ads使用,此处是唯一会修改req中keyword的地方
	if env.GetCID() == cid.VN {
		if len(req.GetKeyword()) > 0 {
			req.Keyword = proto.String(preprocess.UnicodeReplace(req.GetKeyword()))
			traceInfo.QueryKeyword = req.GetKeyword()
		}
		preprocess.VNDistanceLimit(ctx, traceInfo)
		preprocess.VnBrandProtectStoreIds(ctx, traceInfo)
	}
	defer func() {
		rsp.ContextHeaders = traceInfo.RspHeaders
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeServerHandler, traceInfo.HandlerType.String(), "", decision.GetReportHandlerString(traceInfo))
		metric_reporter2.ReportTotalNum(int(rsp.GetTotalNum()), traceinfo.GetTotalNumCategoryTypeStr(handlerType)) // 上报结果 size

		debugInfo.FillProcessInfo(traceInfo)
		rsp.DebugCombineInfo = debugInfo.ToString(ctx, traceInfo)
		rsp.SlaCode = proto.Uint64(traceInfo.SlaCode.GetCode())
		goroutine.WithGo(ctx, "SPFSRSearchTotalNum Log", func(params ...interface{}) {
			reqStr, _ := json.Marshal(req)
			resStr, _ := json.Marshal(rsp)
			logkit.FromContext(ctx).Info("SPFSRSearchTotalNum", zap.String("SearchType", traceInfo.HandlerType.String()),
				zap.String("sla_code", traceInfo.SlaCode.GetCodeStr()),
				zap.Int("cost", int(time.Since(traceInfo.SearchSysTime)/1e6)),
				zap.Any("request", string(reqStr)), zap.Any("response", string(resStr)),
				zap.Bool("real_shadow_flag", traceInfo.ShadowFlag), zap.String("method_downgrade_level", traceInfo.DowngradeLevel))
			traceInfo.DebugInfo = nil
			logger.MyDebug(ctx, traceInfo.IsDebug, "Debug TraceInfo", zap.Any("trace info", traceInfo))
		})
	}()
	err = SearchForTotalNum(ctx, req, rsp, traceInfo, debugInfo)
	return rsp, err
}

func (s *SearchHandler) SearchDishes(ctx context.Context, req *foodalgo_search.SearchRequest) (*foodalgo_search.SearchDishesResp, error) {
	rsp := &foodalgo_search.SearchDishesResp{}
	handlerType := traceinfo.HandlerTypeSearchStoreDishes
	startTime := time.Now()
	// 参数校验
	if len(req.GetKeyword()) == 0 || len(req.GetFilterType().GetStoreIds()) == 0 || req.GetPageSize() > recall.StoreDishRecallSize {
		logkit.FromContext(ctx).Error("SearchDishes params invalid", logkit.String("req", req.String()))
		rsp.ErrCode = proto.Uint64(uint64(errno.ErrParamsInvalid.GetCode()))
		rsp.ErrMsg = proto.String(errno.ErrParamsInvalid.GetMsg())
		return rsp, nil
	}
	// 对 query截断，超过max都不是合理的请求
	if len(req.GetKeyword()) > apollo.GetMaxQueryLen() {
		logkit.FromContext(ctx).Error("SearchDishes params invalid query len", logkit.String("keyword", req.GetKeyword()), logkit.Int("len", len(req.GetKeyword())))
		req.Keyword = proto.String(req.GetKeyword()[:apollo.GetMaxQueryLen()])
	}

	traceInfo := traceinfo.BuildTraceInfo(ctx, req, handlerType)
	defer traceInfo.CloseTraceInfo()
	debugInfo := debuginfo.InitCombineDebugInfo(traceInfo)
	defer func() {
		rsp.ContextHeaders = traceInfo.RspHeaders
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeServerHandler, traceInfo.HandlerType.String(), "", decision.GetReportHandlerString(traceInfo))
		rsp.DebugCombineInfo = debugInfo.ToString(ctx, traceInfo)
		rsp.SlaCode = proto.Uint64(traceInfo.SlaCode.GetCode())
		goroutine.WithGo(ctx, "SearchDishes Log", func(params ...interface{}) {
			reqStr, _ := json.Marshal(req)
			resStr, _ := json.Marshal(rsp)

			logkit.FromContext(ctx).Info("SearchDishes", zap.String("SearchType", traceInfo.HandlerType.String()),
				zap.String("sla_code", traceInfo.SlaCode.GetCodeStr()),
				zap.Int("cost", int(time.Since(traceInfo.SearchSysTime)/1e6)),
				zap.Any("request", string(reqStr)), zap.Any("response", string(resStr)),
				zap.Bool("real_shadow_flag", traceInfo.ShadowFlag), zap.String("method_downgrade_level", traceInfo.DowngradeLevel))
			logger.MyDebug(ctx, traceInfo.IsDebug, "Debug TraceInfo", zap.Any("trace info", traceInfo))
		})
	}()
	err := SearchStoreDishes(ctx, req, rsp, traceInfo, debugInfo)
	// ATP
	errNo := errno.As(err)
	rsp.ErrCode = proto.Uint64(uint64(errNo.GetCode()))
	rsp.ErrMsg = proto.String(errNo.GetMsg())
	if traceInfo.ATP != 0 {
		rsp.Atp = proto.Int(traceInfo.ATP)
	}
	return rsp, nil
}

func (s *SearchHandler) SearchStoresForAffiliate(ctx context.Context, req *foodalgo_search.SearchStoresAffiliateReq) (*foodalgo_search.SearchStoresAffiliateResp, error) {
	rsp := &foodalgo_search.SearchStoresAffiliateResp{}
	handlerType := traceinfo.HandlerTypeSearchStoresForAffiliate
	startTime := time.Now()
	// 参数校验
	if len(req.GetKeyword()) == 0 || req.GetPageSize() > 400 {
		logkit.FromContext(ctx).Error("SearchStoresForAffiliate params invalid", logkit.String("req", req.String()))
		return rsp, nil
	}
	traceInfo := traceinfo.BuildSearchStoresForAffiliateTraceInfo(ctx, req, handlerType)
	defer traceInfo.CloseTraceInfo()
	debugInfo := debuginfo.InitCombineDebugInfo(traceInfo)
	defer func() {
		rsp.ContextHeaders = traceInfo.RspHeaders
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeServerHandler, traceInfo.HandlerType.String(), "", decision.GetReportHandlerString(traceInfo))
		debugInfo.FillProcessInfo(traceInfo)
		rsp.DebugCombineInfo = debugInfo.ToString(ctx, traceInfo)
		rsp.SlaCode = proto.Uint64(traceInfo.SlaCode.GetCode())
		goroutine.WithGo(ctx, "SearchStoresForAffiliate Log", func(params ...interface{}) {
			reqStr, _ := json.Marshal(req)
			resStr, _ := json.Marshal(rsp)

			logkit.FromContext(ctx).Info("SearchStoresForAffiliate", zap.String("SearchType", traceInfo.HandlerType.String()),
				zap.String("sla_code", traceInfo.SlaCode.GetCodeStr()),
				zap.Int("cost", int(time.Since(traceInfo.SearchSysTime)/1e6)),
				zap.Any("request", string(reqStr)), zap.Any("response", string(resStr)),
				zap.Bool("real_shadow_flag", traceInfo.ShadowFlag), zap.String("method_downgrade_level", traceInfo.DowngradeLevel))
			logger.MyDebug(ctx, traceInfo.IsDebug, "Debug TraceInfo", zap.Any("trace info", traceInfo))
		})
	}()
	err := SearchStoresForAffiliate(ctx, req, rsp, traceInfo, debugInfo)
	return rsp, err
}

func (s *SearchHandler) SearchDishesForAffiliate(ctx context.Context, req *foodalgo_search.SearchDishesAffiliateReq) (*foodalgo_search.SearchDishesAffiliateResp, error) {
	rsp := &foodalgo_search.SearchDishesAffiliateResp{}
	handlerType := traceinfo.HandlerTypeSearchDishesForAffiliate
	startTime := time.Now()
	// 参数校验
	if len(req.GetKeyword()) == 0 || req.GetStoreId() == 0 || req.GetPageSize() > recall.StoreDishRecallSize {
		logkit.FromContext(ctx).Error("SearchDishesForAffiliate params invalid", logkit.String("req", req.String()))
		return rsp, nil
	}
	traceInfo := traceinfo.BuildSearchDishesForAffiliateTraceInfo(ctx, req, handlerType)
	defer traceInfo.CloseTraceInfo()
	debugInfo := debuginfo.InitCombineDebugInfo(traceInfo)
	defer func() {
		rsp.ContextHeaders = traceInfo.RspHeaders
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeServerHandler, traceInfo.HandlerType.String(), "", decision.GetReportHandlerString(traceInfo))
		rsp.DebugCombineInfo = debugInfo.ToString(ctx, traceInfo)
		rsp.SlaCode = proto.Uint64(traceInfo.SlaCode.GetCode())
		goroutine.WithGo(ctx, "SearchDishesForAffiliate Log", func(params ...interface{}) {
			reqStr, _ := json.Marshal(req)
			resStr, _ := json.Marshal(rsp)

			logkit.FromContext(ctx).Info("SearchDishesForAffiliate", zap.String("SearchType", traceInfo.HandlerType.String()),
				zap.String("sla_code", traceInfo.SlaCode.GetCodeStr()),
				zap.Int("cost", int(time.Since(traceInfo.SearchSysTime)/1e6)),
				zap.Any("request", string(reqStr)), zap.Any("response", string(resStr)),
				zap.Bool("real_shadow_flag", traceInfo.ShadowFlag), zap.String("method_downgrade_level", traceInfo.DowngradeLevel))
			logger.MyDebug(ctx, traceInfo.IsDebug, "Debug TraceInfo", zap.Any("trace info", traceInfo))
		})
	}()
	err := SearchDishesForAffiliate(ctx, req, rsp, traceInfo, debugInfo)
	return rsp, err
}

func (s *SearchHandler) SearchHistoryOrders(ctx context.Context, req *foodalgo_search.SearchHistoryOrderRequest) (*foodalgo_search.SearchHistoryOrderResponse, error) {
	rsp := &foodalgo_search.SearchHistoryOrderResponse{}
	handlerType := traceinfo.HandlerTypeSearchHistoryOrder
	startTime := time.Now()
	// 参数校验
	keyword := strings.TrimSpace(req.GetKeyword())
	if len(keyword) == 0 || (req.GetNowUid() == 0 && req.GetShopeeUid() == 0) || req.GetPageSize() > 200 {
		logkit.FromContext(ctx).Error("SearchHistoryOrder params invalid", logkit.String("req", req.String()))
		rsp.ErrCode = proto.Uint64(uint64(errno.ErrParamsInvalid.GetCode()))
		rsp.ErrMsg = proto.String(errno.ErrParamsInvalid.GetMsg())
		return rsp, nil
	}

	// id/th/my 用虾皮UID， VN用 nowUid
	userId := req.GetShopeeUid()
	if cid.IsVN() {
		userId = req.GetNowUid()
	}
	reqTran := &foodalgo_search.SearchRequest{
		Keyword:      proto.String(keyword),
		IsDebug:      req.IsDebug,
		IsCloseCache: req.IsCloseCache,
		PublishId:    req.PublishId,
		BuyerId:      proto.Uint64(userId),
	}

	traceInfo := traceinfo.BuildTraceInfo(ctx, reqTran, handlerType)
	defer traceInfo.CloseTraceInfo()
	debugInfo := debuginfo.InitCombineDebugInfo(traceInfo)
	defer func() {
		rsp.ContextHeaders = traceInfo.RspHeaders
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeServerHandler, traceInfo.HandlerType.String(), "", decision.GetReportHandlerString(traceInfo))
		debugInfo.FillProcessInfo(traceInfo)
		rsp.DebugCombineInfo = debugInfo.ToString(ctx, traceInfo)
		rsp.SlaCode = proto.Uint64(traceInfo.SlaCode.GetCode())
		goroutine.WithGo(ctx, "SearchHistoryOrder Log", func(params ...interface{}) {
			reqStr, _ := json.Marshal(req)
			resStr, _ := json.Marshal(rsp)

			logkit.FromContext(ctx).Info("SearchHistoryOrder", zap.String("SearchType", traceInfo.HandlerType.String()),
				zap.String("sla_code", traceInfo.SlaCode.GetCodeStr()),
				zap.Int("cost", int(time.Since(traceInfo.SearchSysTime)/1e6)),
				zap.Any("request", string(reqStr)), zap.Any("response", string(resStr)),
				zap.Bool("real_shadow_flag", traceInfo.ShadowFlag), zap.String("method_downgrade_level", traceInfo.DowngradeLevel))
			logger.MyDebug(ctx, traceInfo.IsDebug, "Debug TraceInfo", zap.Any("trace info", traceInfo))
		})
	}()

	err := SearchHistoryOrders(ctx, req, reqTran, rsp, traceInfo)
	errNo := errno.As(err) // ATP
	rsp.ErrCode = proto.Uint64(uint64(errNo.GetCode()))
	rsp.ErrMsg = proto.String(errNo.GetMsg())
	if traceInfo.ATP != 0 {
		rsp.Atp = proto.Int(traceInfo.ATP)
	}
	return rsp, nil
}

func (s *SearchHandler) SearchStoresWithListingDish(ctx context.Context, req *foodalgo_search.SearchStoresWithListingDishReq) (*foodalgo_search.SearchStoresWithListingDishResp, error) {
	rsp := &foodalgo_search.SearchStoresWithListingDishResp{}
	startTime := time.Now()
	handlerType := traceinfo.HandlerTypeSearchStoresWithListingDish
	// 参数校验
	err, invalid := paramcheck.DishListingParamCheck(ctx, req)
	if invalid {
		rsp.ErrCode = proto.Uint64(uint64(errno.ErrParamsInvalid.GetCode()))
		rsp.ErrMsg = proto.String(errno.ErrParamsInvalid.GetMsg())
		return rsp, nil
	}
	// 对 query截断，超过max都不是合理的请求
	if len(req.GetKeyword()) > apollo.GetMaxQueryLen() {
		logkit.FromContext(ctx).Error("Search params invalid query len", logkit.String("keyword", req.GetKeyword()), logkit.Int("len", len(req.GetKeyword())))
		req.Keyword = proto.String(req.GetKeyword()[:apollo.GetMaxQueryLen()])
	}

	traceInfo := traceinfo.BuildSSearchStoresWithListingDishTraceInfo(ctx, req, handlerType)
	defer traceInfo.CloseTraceInfo()

	debugInfo := debuginfo.InitCombineDebugInfo(traceInfo)
	// 广告查询词也需要处理，只能放在最外层，不能放到preProcessor. 为了给ads使用,此处是唯一会修改req中keyword的地方 todo 代码格式优化
	if env.GetCID() == cid.VN {
		if len(req.GetKeyword()) > 0 {
			req.Keyword = proto.String(preprocess.UnicodeReplace(req.GetKeyword()))
			traceInfo.QueryKeyword = req.GetKeyword()
		}
		preprocess.VNDistanceLimit(ctx, traceInfo)
		preprocess.VnBrandProtectStoreIds(ctx, traceInfo)
	}
	defer func() {
		rsp.ContextHeaders = traceInfo.RspHeaders
		if len(rsp.GetStores()) > 0 {
			rsp.ErrMsg = proto.String(errno.Ok.Msg)
			rsp.ErrCode = proto.Uint64(uint64(errno.Ok.Code))
		}
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeServerHandler, traceInfo.HandlerType.String(), "", decision.GetReportHandlerString(traceInfo))
		storeNum, dishNum, adsStoreNum, adsDishNum := getItemNumsDishListing(rsp.GetStores())
		metric_reporter2.ReportFinalItemNum(storeNum, dishNum, adsStoreNum, adsDishNum, metric_reporter2.SearchReportTypeResponse, traceInfo.HandlerType.String(), "")

		debugInfo.FillProcessInfo(traceInfo)
		rsp.DebugCombineInfo = debugInfo.ToString(ctx, traceInfo)
		rsp.SlaCode = proto.Uint64(traceInfo.SlaCode.GetCode())
		goroutine.WithGo(ctx, "SPFSRSearch Log", func(params ...interface{}) {
			reqStr, _ := json.Marshal(req)
			resStr, _ := json.Marshal(rsp)

			logkit.FromContext(ctx).Info("SPFSRSearch", zap.String("SearchType", traceInfo.HandlerType.String()),
				zap.String("sla_code", traceInfo.SlaCode.GetCodeStr()),
				zap.Int("cost", int(time.Since(traceInfo.SearchSysTime)/1e6)),
				zap.Any("request", string(reqStr)), zap.Any("response", string(resStr)), zap.Any("ATP", traceInfo.ATP),
				zap.Bool("real_shadow_flag", traceInfo.ShadowFlag), zap.String("method_downgrade_level", traceInfo.DowngradeLevel))
			if traceInfo.IsDebug {
				traceInfo.DebugInfo = nil
				logger.MyDebug(ctx, traceInfo.IsDebug, "Debug TraceInfo", zap.Any("trace info", traceInfo))
			}
		})
	}()

	err = SearchStoresWishDishListing(ctx, rsp, traceInfo, debugInfo)
	errNo := errno.As(err)
	rsp.ErrCode = proto.Uint64(uint64(errNo.GetCode()))
	rsp.ErrMsg = proto.String(errNo.GetMsg())
	return rsp, nil

}

func (s *SearchHandler) SearchCollectionWithListingDish(ctx context.Context, req *foodalgo_search.SearchStoresWithListingDishReq) (*foodalgo_search.SearchStoresWithListingDishResp, error) {
	rsp := &foodalgo_search.SearchStoresWithListingDishResp{}
	startTime := time.Now()
	handlerType := traceinfo.HandlerTypeSearchCollectionWithListingDish
	// 参数校验
	err, invalid := paramcheck.DishListingParamCheck(ctx, req)
	if invalid {
		rsp.ErrCode = proto.Uint64(uint64(errno.ErrParamsInvalid.GetCode()))
		rsp.ErrMsg = proto.String(errno.ErrParamsInvalid.GetMsg())
		return rsp, nil
	}
	// 对 query截断，超过max都不是合理的请求
	if len(req.GetKeyword()) > apollo.GetMaxQueryLen() {
		logkit.FromContext(ctx).Error("Search params invalid query len", logkit.String("keyword", req.GetKeyword()), logkit.Int("len", len(req.GetKeyword())))
		req.Keyword = proto.String(req.GetKeyword()[:apollo.GetMaxQueryLen()])
	}

	traceInfo := traceinfo.BuildSSearchStoresWithListingDishTraceInfo(ctx, req, handlerType)
	defer traceInfo.CloseTraceInfo()

	debugInfo := debuginfo.InitCombineDebugInfo(traceInfo)
	// 广告查询词也需要处理，只能放在最外层，不能放到preProcessor. 为了给ads使用,此处是唯一会修改req中keyword的地方 todo 代码格式优化
	if env.GetCID() == cid.VN {
		if len(req.GetKeyword()) > 0 {
			req.Keyword = proto.String(preprocess.UnicodeReplace(req.GetKeyword()))
			traceInfo.QueryKeyword = req.GetKeyword()
		}
		preprocess.VNDistanceLimit(ctx, traceInfo)
		preprocess.VnBrandProtectStoreIds(ctx, traceInfo)
	}
	defer func() {
		rsp.ContextHeaders = traceInfo.RspHeaders
		if len(rsp.GetStores()) > 0 {
			rsp.ErrMsg = proto.String(errno.Ok.Msg)
			rsp.ErrCode = proto.Uint64(uint64(errno.Ok.Code))
		}
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeServerHandler, traceInfo.HandlerType.String(), "", decision.GetReportHandlerString(traceInfo))

		debugInfo.FillProcessInfo(traceInfo)
		rsp.DebugCombineInfo = debugInfo.ToString(ctx, traceInfo)
		rsp.SlaCode = proto.Uint64(traceInfo.SlaCode.GetCode())
		goroutine.WithGo(ctx, "SPFSRSearch Log", func(params ...interface{}) {
			reqStr, _ := json.Marshal(req)
			resStr, _ := json.Marshal(rsp)

			logkit.FromContext(ctx).Info("SPFSRSearch", zap.String("SearchType", traceInfo.HandlerType.String()),
				zap.String("sla_code", traceInfo.SlaCode.GetCodeStr()),
				zap.Int("cost", int(time.Since(traceInfo.SearchSysTime)/1e6)),
				zap.Any("request", string(reqStr)), zap.Any("response", string(resStr)), zap.Any("ATP", traceInfo.ATP),
				zap.Bool("real_shadow_flag", traceInfo.ShadowFlag), zap.String("method_downgrade_level", traceInfo.DowngradeLevel))
			if traceInfo.IsDebug {
				traceInfo.DebugInfo = nil
				logger.MyDebug(ctx, traceInfo.IsDebug, "Debug TraceInfo", zap.Any("trace info", traceInfo))
			}
		})
	}()

	err = SearchCollectionWishDishListing(ctx, rsp, traceInfo, debugInfo)
	errNo := errno.As(err)
	rsp.ErrCode = proto.Uint64(uint64(errNo.GetCode()))
	rsp.ErrMsg = proto.String(errNo.GetMsg())
	return rsp, nil
}
