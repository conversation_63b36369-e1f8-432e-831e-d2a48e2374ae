package search_limiter

import (
	"context"
	"fmt"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/rate-limit/constant"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/rate-limit/limiter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/rate-limit/register"
)

const (
	group                      = "foody_and_local_service_intelligence"
	project                    = "food_search_and_recommend"
	gatewayModule              = "o2oalgo_gateway_config.xml"
	MethodDownGradeNotConfig   = "method_down_grade_not_config"
	MethodDownGradeNoNeed      = "method_down_grade_no_need"
	gatewayModuleNonLiveSecret = "a233b703b3aceff240930a1274d80fa0b0f9958fecaeb200a93ecb89cdb46a96"
	gatewayModuleLiveSecret    = "cfaaa328686ef7ae1c122126081b7799bc96d9148c12a3e900fa5a413ab05683"
)

var MethodDownGrades = []constant.LimitType{constant.MethodDownGrade1, constant.MethodDownGrade2, constant.MethodDownGrade3, constant.MethodDownGrade4}

type RateLimiterService struct {
}

func (r *RateLimiterService) Allow(method string) bool {

	rateLimiter := limiter.GetRateLimiter(constant.Method, method)
	if rateLimiter != nil {
		if !rateLimiter.Allow() {
			metric_reporter.ReportCounter(metric_reporter.ReportServiceRateLimitRequestMetricName, 1, reporter.Label{
				Key: "method",
				Val: method,
			})
			logkit.Error("limiter reject request", logkit.String("method", method))
			return false
		} else {
			return true
		}
	} else {
		logkit.Info("no available current limiter was obtained")
	}
	return false
}

func (r *RateLimiterService) DownGradeAllow(ctx context.Context, method string) []string {
	res := make([]string, len(MethodDownGrades), len(MethodDownGrades))
	wg := sync.WaitGroup{}
	wg.Add(len(MethodDownGrades))
	for index, MethodDownGrade := range MethodDownGrades {
		goroutine.WithGo(ctx, "DownGradeAllow", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			i := param[0].(int)
			name := param[1].(constant.LimitType)
			rateLimiter := limiter.GetRateLimiter(name, method)
			if rateLimiter != nil {
				if !rateLimiter.Allow() {
					metric_reporter.ReportCounter(metric_reporter.ReportServiceRateLimitRequestMetricName, 1, reporter.Label{
						Key: "method",
						Val: fmt.Sprintf("%v-%v", string(name), method),
					})
					// logkit.Error("limiter request", logkit.String("method", method), logkit.String("LimitType", string(name)))
					res[i] = string(name)
				} else {
					res[i] = MethodDownGradeNoNeed
				}
			} else {
				logkit.Debug(string(name) + "no available current limiter was obtained")
				res[i] = MethodDownGradeNotConfig
			}
		}, index, MethodDownGrade)
	}
	wg.Wait()
	return res
}

func (r *RateLimiterService) AllowV2(method string, req any, limitParam func(any) []string) bool {
	// 先限流子场景，避免来自单一source的流量将接口的限流打满，导致走不到子场景限流的代码逻辑
	subLimitTypes := limitParam(req)
	if len(subLimitTypes) > 0 {
		for _, limitType := range subLimitTypes {
			subMethodLimit := fmt.Sprintf("%s-%s", method, limitType)
			subRateLimiter := limiter.GetRateLimiter(constant.Method, subMethodLimit)
			if subRateLimiter == nil {
				continue
			}
			if !subRateLimiter.Allow() {
				metric_reporter.ReportCounter(metric_reporter.ReportServiceRateLimitRequestMetricName, 1, reporter.Label{
					Key: "method",
					Val: subMethodLimit,
				})
				logkit.Error("limiter reject request", logkit.String("method", subMethodLimit))
				return false
			}
		}
	}
	rateLimiter := limiter.GetRateLimiter(constant.Method, method)
	if rateLimiter == nil {
		logkit.Info("no available current limiter was obtained", logkit.String("method", method))
		return false
	}
	if !rateLimiter.Allow() {
		metric_reporter.ReportCounter(metric_reporter.ReportServiceRateLimitRequestMetricName, 1, reporter.Label{
			Key: "method",
			Val: method,
		})
		logkit.Error("limiter reject request", logkit.String("method", method))
		return false
	}
	return true
}
func (r *RateLimiterService) SubDownGradeAllow(ctx context.Context, method string, matchRes []string, req any, limitParam func(any) []string) []string {
	subMatchRes := make([]string, len(MethodDownGrades), len(MethodDownGrades))
	subLimitTypes := limitParam(req)
	if len(subLimitTypes) == 0 {
		return matchRes
	}
	for _, limitType := range subLimitTypes {
		rateLimiterMethodName := fmt.Sprintf("%s-%s", method, limitType)
		for i, name := range MethodDownGrades {
			rateLimiter := limiter.GetRateLimiter(name, rateLimiterMethodName)
			if rateLimiter != nil {
				if !rateLimiter.Allow() {
					metric_reporter.ReportCounter(metric_reporter.ReportServiceRateLimitRequestMetricName, 1, reporter.Label{
						Key: "method",
						Val: fmt.Sprintf("%v-%v", string(name), rateLimiterMethodName),
					})
					// logkit.Error("limiter request", logkit.String("method", rateLimiterMethodName), logkit.String("LimitType", string(name)))
					subMatchRes[i] = string(name)
				} else {
					if subMatchRes[i] == "" {
						subMatchRes[i] = MethodDownGradeNoNeed
					}
				}
			} else {
				if subMatchRes[i] == "" {
					subMatchRes[i] = MethodDownGradeNotConfig
				}
			}
		}
	}
	// 如果接口满足downgrade4降级指标，则子场景无条件赋予downgrade4降级指标
	// 其他情况优先以子场景的降级配置为主
	if len(matchRes) == 4 && matchRes[3] == constant.MethodDownGrade4 {
		subMatchRes[3] = constant.MethodDownGrade4
	}
	// 兜底 如果子场景的降级有流量，但是没配置，这种情况下使用接口的降级指标
	abortSubMatchRes := true
	for _, match := range subMatchRes {
		if match != MethodDownGradeNotConfig {
			abortSubMatchRes = false
		}
	}
	if abortSubMatchRes {
		return matchRes
	}
	return subMatchRes
}

func InitRateLimitConfig(source string) *RateLimiterService {
	gatewayModuleCurrentSecret := gatewayModuleNonLiveSecret
	if env.Environment() == "live" || env.Environment() == "liveish" {
		gatewayModuleCurrentSecret = gatewayModuleLiveSecret
	}
	if err := register.InitRateLimitConfig(group, project, gatewayModule, gatewayModuleCurrentSecret, env.Region(), source); err != nil {
		logkit.Error("failed to init rate limit", logkit.Err(err))
		panic(err)
	}
	return &RateLimiterService{}
}
