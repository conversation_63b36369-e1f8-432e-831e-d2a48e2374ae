package filter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"sort"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func Filter(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo) []*model.StoreInfo {
	pt := time.Now()
	var res []*model.StoreInfo
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseStoreFilter, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFilter, len(res))
	}()

	nearDistance := traceInfo.AbParamClient.GetParamWithInt("Search.General.Monitoring.Distance", 0)

	// 过滤前的监控上报
	traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreCntOpeningStoreBeforeFilter, stores.GetOpenStatusCnt())
	traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreCntNearDistanceBeforeFilter+fmt.Sprintf("_%d", nearDistance), stores.GetNearDistanceCnt(float64(nearDistance)))
	traceInfo.AddPhraseStoreLength(ctx, traceinfo.AvgDistanceBeforeFilter, int(stores.GetAvgDistance()))

	// 通用过滤
	stores = StoreIdsFilter(ctx, traceInfo, stores)
	stores = OpeningFilter(ctx, traceInfo, stores)

	if cid.IsVNRegion() {
		res = VNSearchFilter(ctx, stores, traceInfo)
	} else {
		res = SearchFilter(ctx, stores, traceInfo)
	}

	// 过滤后的监控上报
	traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreCntOpeningStoreAfterFilter, stores.GetOpenStatusCnt())
	traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreCntNearDistanceAfterFilter+fmt.Sprintf("_%d", nearDistance), stores.GetNearDistanceCnt(float64(nearDistance)))
	traceInfo.AddPhraseStoreLength(ctx, traceinfo.AvgDistanceAfterFilter, int(stores.GetAvgDistance()))

	// diff mock score情况下，保证有序
	if decision.IsMockModelScore(ctx, traceInfo) {
		sort.Slice(res, func(i, j int) bool {
			return res[i].StoreId < res[j].StoreId
		})
	}
	return res
}

func AffiliateFilter(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo) []*model.StoreInfo {
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseStoreFilter, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFilter, len(stores))
	}()

	// 各地区统一过滤逻辑
	stores = StoreIdsFilter(ctx, traceInfo, stores)
	stores = StoresWithoutCommissionFilter(ctx, traceInfo, stores)
	stores = StatusFilter(traceInfo, stores)
	stores = IsPreferredMerchantFilter(traceInfo, traceInfo.TraceRequest.GetFilterType(), stores)
	stores = FilterByL2Category(ctx, traceInfo, stores)
	stores = CommissionRateFilter(traceInfo, traceInfo.TraceRequest.GetFilterType(), stores)
	stores = RatingScoreFilter(ctx, traceInfo, stores) // vn 站内搜索没有，但是Affiliate 首页有，所以这次也一起支持
	// 各地区差异过滤逻辑
	if cid.IsVNRegion() {
		stores = PromotionFilterVN(ctx, traceInfo, stores)
		stores = OpeningFilter(ctx, traceInfo, stores)
		stores = FilterByCategoryType(ctx, traceInfo, stores)
	} else {
		stores = InactiveDistrictFilter(ctx, traceInfo, stores)
		stores = ShopTypeFilter(ctx, traceInfo, stores)
		stores = FilterByStoreTags(ctx, traceInfo, stores)
		stores = FilterByStorePrice(ctx, traceInfo, stores)
		stores = FilterByHalalType(ctx, traceInfo, stores) // 仅MY
		stores = FilterByOrderType(ctx, traceInfo, stores)
		stores = PromotionFilter(ctx, traceInfo, stores)
		stores = FilterByStorePromotion(ctx, traceInfo, stores)
	}
	stores = FilterStoreBlacklist(ctx, traceInfo, stores)
	return stores
}

func VNSearchFilter(ctx context.Context, stores []*model.StoreInfo, traceInfo *traceinfo.TraceInfo) []*model.StoreInfo {
	if !traceInfo.IsDowngradeDataServer {
		stores = OpeningFilter(ctx, traceInfo, stores)
		stores = DistanceFilterVN(ctx, traceInfo, stores, traceInfo.DistanceLimit)
		stores = CityFilter(traceInfo, stores, traceInfo.TraceRequest.CityId)
		stores = DistrictFilter(traceInfo, stores, traceInfo.TraceRequest.DistrictIds)
		stores = FilterByCategoryType(ctx, traceInfo, stores)
	}
	stores = PromotionFilterVN(ctx, traceInfo, stores)
	stores = FilterStoreBlacklist(ctx, traceInfo, stores)
	return stores
}

func SearchFilter(ctx context.Context, stores []*model.StoreInfo, traceInfo *traceinfo.TraceInfo) []*model.StoreInfo {
	// 正排降级时不进行过滤
	if !traceInfo.IsDowngradeDataServer {
		stores = StatusFilter(traceInfo, stores)
		stores = IsPreferredMerchantFilter(traceInfo, traceInfo.TraceRequest.FilterType, stores)
		stores = StorePolygonDistanceFilter(ctx, traceInfo, stores)
		stores = DishSalesNumFilter(ctx, traceInfo, stores)
		if env.GetCID() == cid.TH || env.GetCID() == cid.MY {
			stores = DishFilter(ctx, traceInfo, stores) // ID菜品过滤在下沉召菜后实现
		}
		stores = InactiveDistrictFilter(ctx, traceInfo, stores)
		stores = DuplicateMerchantFilter(ctx, traceInfo, stores)
		stores = DuplicateBrandFilter(ctx, traceInfo, stores)
		stores = RatingScoreFilter(ctx, traceInfo, stores)
		stores = ShopTypeFilter(ctx, traceInfo, stores)
		stores = FilterByL2Category(ctx, traceInfo, stores)
		stores = FilterByStoreTags(ctx, traceInfo, stores)
		stores = FilterByShippingDistance(ctx, traceInfo, stores)
		stores = FilterByStorePrice(ctx, traceInfo, stores)
		stores = FilterByHalalType(ctx, traceInfo, stores)
		stores = FilterByOrderType(ctx, traceInfo, stores)
		stores = FilterNonHalalStoreWhenRamadan(ctx, traceInfo, stores)
		stores = FilterByShippingFee(ctx, traceInfo, stores)
		stores = FilterStoreByRecallConfigDistance(ctx, traceInfo, stores)
	} else {
		logkit.FromContext(ctx).Error("ignore filterStoreIDs, filterInactiveDistrict, filterDuplicateBrand")
	}
	// 店铺评分 & promotion 筛选
	stores = PromotionFilter(ctx, traceInfo, stores)
	stores = FilterByShippingFee(ctx, traceInfo, stores)
	stores = FilterByStorePromotion(ctx, traceInfo, stores)
	stores = FilterStoreBlacklist(ctx, traceInfo, stores)
	return stores
}

func SearchMainSiteFilter(ctx context.Context, stores []*model.StoreInfo, traceInfo *traceinfo.TraceInfo) []*model.StoreInfo {
	// 通用过滤
	stores = StoreIdsFilter(ctx, traceInfo, stores)
	stores = OpeningFilter(ctx, traceInfo, stores)

	// 正排降级时不进行过滤
	if !traceInfo.IsDowngradeDataServer {
		if cid.IsVNRegion() {
			stores = DuplicateBrandFilter(ctx, traceInfo, stores)
			stores = DistanceFilterVNMainSite(ctx, traceInfo, stores, traceInfo.DistanceLimit)
		} else {
			stores = StorePolygonDistanceFilter(ctx, traceInfo, stores)
			stores = InactiveDistrictFilter(ctx, traceInfo, stores)
			stores = DuplicateBrandFilter(ctx, traceInfo, stores)
		}
	} else {
		logkit.FromContext(ctx).Error("ignore filterStoreIDs, filterInactiveDistrict, filterDuplicateBrand")
	}

	stores = FilterStoreBlacklist(ctx, traceInfo, stores)
	return stores
}
